﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddTrainingHistoryAndStatusUpdates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "EndDate",
                table: "Trainings",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ValidityPeriodMonths",
                table: "Trainings",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "WorkerTrainingHistory",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    WorkerId = table.Column<int>(type: "int", nullable: false),
                    TrainingId = table.Column<int>(type: "int", nullable: false),
                    CompletionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CertificateUrl = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Score = table.Column<double>(type: "float(5)", precision: 5, scale: 2, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkerTrainingHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkerTrainingHistory_Trainings_TrainingId",
                        column: x => x.TrainingId,
                        principalTable: "Trainings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkerTrainingHistory_Workers_WorkerId",
                        column: x => x.WorkerId,
                        principalTable: "Workers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_CompletionDate",
                table: "WorkerTrainingHistory",
                column: "CompletionDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_ExpiryDate",
                table: "WorkerTrainingHistory",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_Status",
                table: "WorkerTrainingHistory",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_TrainingId",
                table: "WorkerTrainingHistory",
                column: "TrainingId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_WorkerId",
                table: "WorkerTrainingHistory",
                column: "WorkerId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingHistory_WorkerId_TrainingId_CompletionDate",
                table: "WorkerTrainingHistory",
                columns: new[] { "WorkerId", "TrainingId", "CompletionDate" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WorkerTrainingHistory");

            migrationBuilder.DropColumn(
                name: "EndDate",
                table: "Trainings");

            migrationBuilder.DropColumn(
                name: "ValidityPeriodMonths",
                table: "Trainings");
        }
    }
}
