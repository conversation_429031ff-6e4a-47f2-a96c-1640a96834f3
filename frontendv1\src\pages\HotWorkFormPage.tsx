import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, X } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { hotWorkPermitData, hotWorkPermitDescription } from '../utils/hotWorksPTW';

interface HotWorkFormData {
  serialNumber: string;
  formData: { [key: string]: any };
  signoffData: Array<{ [key: string]: any }>;
}

const HotWorkFormPage: React.FC = () => {
  const {} = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate random serial number
  const [serialNumber] = useState(() => Math.floor(Math.random() * 1000000).toString());

  const [formData, setFormData] = useState<HotWorkFormData>({
    serialNumber,
    formData: {
      // Auto-populate date and time fields
      'Details_Starting from': new Date().toISOString().slice(0, 16),
      'Details_Ending at': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 24 hours later
    },
    signoffData: [
      { Name: '', Designation: '', Signature: '', Time: '' },
      { Name: '', Designation: '', Signature: '', Time: '' },
      { Name: '', Designation: '', Signature: '', Time: '' }
    ]
  });

  const handleInputChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      formData: {
        ...prev.formData,
        [key]: value
      }
    }));
  };

  const handleTableChange = (rowIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      signoffData: prev.signoffData.map((row, index) =>
        index === rowIndex ? { ...row, [field]: value } : row
      )
    }));
  };

  const addTableRow = () => {
    const newRow = { Name: '', Designation: '', Signature: '', Time: '' };
    setFormData(prev => ({
      ...prev,
      signoffData: [...prev.signoffData, newRow]
    }));
  };

  const removeTableRow = (rowIndex: number) => {
    if (formData.signoffData.length > 1) {
      setFormData(prev => ({
        ...prev,
        signoffData: prev.signoffData.filter((_, index) => index !== rowIndex)
      }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Hot Work Form submitted:', formData);
      alert('Hot Work permit form submitted successfully!');
      navigate(-1);
    } catch (error) {
      console.error('Error submitting Hot Work form:', error);
      alert('Error submitting form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field: any, sectionKey: string) => {
    const fieldKey = `${sectionKey}_${field.name}`;
    const value = formData.formData[fieldKey] || '';

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            required={field.required}
          />
        );
      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            required={field.required}
          />
        );
      case 'datetime':
        return (
          <input
            type="datetime-local"
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 bg-gray-100"
            required={field.required}
            readOnly
          />
        );
      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 bg-gray-100"
            required={field.required}
            readOnly
          />
        );
      case 'time':
        return (
          <input
            type="time"
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 bg-gray-100"
            required={field.required}
            readOnly
          />
        );
      case 'checkbox':
        return (
          <input
            type="checkbox"
            checked={value || false}
            onChange={(e) => handleInputChange(fieldKey, e.target.checked)}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            required={field.required}
          />
        );
      case 'radio':
        return (
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name={fieldKey}
                value="Yes"
                checked={value === 'Yes'}
                onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span className="ml-2 text-sm">Yes</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name={fieldKey}
                value="No"
                checked={value === 'No'}
                onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span className="ml-2 text-sm">No</span>
            </label>
          </div>
        );
      case 'signature':
        return (
          <div className="border border-gray-300 rounded-md p-2 bg-gray-50 text-center text-gray-500 text-xs">
            Signature Pad
          </div>
        );
      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => handleInputChange(fieldKey, e.target.value)}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            required={field.required}
          />
        );
    }
  };

  return (
    <FloatingCard title="Hot Work Form">
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hot Work Form</h1>
                <p className="text-xs text-gray-500">{hotWorkPermitDescription}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="w-full p-4">
          <div className="space-y-3">
            {/* Header Section */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-2">
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-1">
                  <h2 className="text-base font-bold text-red-800">
                    HOT WORK PERMIT
                  </h2>
                  <span className="text-xs text-gray-500 font-light">Serial No: {formData.serialNumber}</span>
                </div>
                <div className="text-xs text-red-600 font-medium">
                  This permit is valid for one day only.
                </div>
              </div>
            </div>

            {/* Form Sections */}
            {hotWorkPermitData.map((section, sectionIndex) => {
              // Handle signoff section first (it has a different structure)
              if (section.type === 'signoff') {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{section.title}</h3>
                    {section.description && (
                      <p className="text-xs text-gray-600 mb-3">{section.description}</p>
                    )}

                    {/* Additional fields */}
                    {section.items && section.items.length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                        {section.items.map((item: any, itemIndex: number) => (
                          <div key={itemIndex}>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              {item.name} {item.required && '*'}
                            </label>
                            {renderField(item, section.title)}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Signoff Table */}
                    <div className="overflow-x-auto">
                      <table className="min-w-full border border-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                              S/No
                            </th>
                            {section.tableHeader?.map((header: any, headerIndex: number) => (
                              <th key={headerIndex} className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                {header.name}
                              </th>
                            ))}
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Action
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {formData.signoffData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 border-r border-gray-300">
                                {rowIndex + 1}
                              </td>
                              {section.tableHeader?.map((header: any, headerIndex: number) => (
                                <td key={headerIndex} className="px-2 py-1 whitespace-nowrap border-r border-gray-300">
                                  {header.type === 'signature' ? (
                                    <div className="w-20 h-6 border border-gray-300 rounded bg-gray-50 text-xs text-center leading-6 text-gray-500">
                                      Sign
                                    </div>
                                  ) : header.type === 'time' ? (
                                    <input
                                      type="time"
                                      value={row[header.name] || ''}
                                      onChange={(e) => handleTableChange(rowIndex, header.name, e.target.value)}
                                      className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                                    />
                                  ) : (
                                    <input
                                      type="text"
                                      value={row[header.name] || ''}
                                      onChange={(e) => handleTableChange(rowIndex, header.name, e.target.value)}
                                      className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                                    />
                                  )}
                                </td>
                              ))}
                              <td className="px-2 py-1 whitespace-nowrap">
                                <button
                                  onClick={() => removeTableRow(rowIndex)}
                                  className="text-red-600 hover:text-red-800"
                                  disabled={formData.signoffData.length <= 1}
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <button
                      onClick={addTableRow}
                      className="mt-2 px-3 py-1 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200"
                    >
                      Add Row
                    </button>
                  </div>
                );
              }

              // Handle special radio section
              if (section.type === 'radio') {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <div className="mb-3">
                      <label className="block text-base font-semibold text-gray-900 mb-2">
                        {section.name}
                      </label>
                      {renderField(section, 'radio_section')}
                      {section.lastDetails && (
                        <p className="text-xs text-gray-600 mt-2 italic">{section.lastDetails}</p>
                      )}
                    </div>
                  </div>
                );
              }

              // Handle special sections with description and items (Fire watch, Fire Marshal)
              if (section.description && section.items && !section.items[0].hasOwnProperty(Object.keys(section.items[0])[0])) {
                const sectionKey = Object.keys(section)[0];
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{sectionKey}</h3>
                    <p className="text-xs text-gray-600 mb-3">{section.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {section.items.map((field: any, fieldIndex: number) => (
                        <div key={fieldIndex}>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            {field.name} {field.required && '*'}
                          </label>
                          {renderField(field, sectionKey)}
                          {(field.type === 'date' || field.type === 'time') && (
                            <p className="text-xs text-gray-500 mt-1">Auto-generated</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }

              // Handle Permit Issue and Permit Return sections (with nested role-based items)
              if (section.description && section.items && section.items[0].hasOwnProperty(Object.keys(section.items[0])[0])) {
                const sectionKey = Object.keys(section)[0];
                const permitData = section;

                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{sectionKey}</h3>
                    <p className="text-xs text-gray-600 mb-3">{permitData.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {permitData.items.map((roleSection: any, roleIndex: number) => {
                        const roleKey = Object.keys(roleSection)[0];
                        const roleFields = roleSection[roleKey];

                        return (
                          <div key={roleIndex} className="border border-gray-200 rounded p-3">
                            <h4 className="text-xs font-medium text-gray-800 mb-2">{roleKey}</h4>
                            <div className="space-y-2">
                              {roleFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.required && '*'}
                                  </label>
                                  {renderField(field, `${sectionKey}_${roleKey}`)}
                                  {(field.type === 'date' || field.type === 'time') && (
                                    <p className="text-xs text-gray-500 mt-1">Auto-generated</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              // Handle regular sections with field arrays
              const sectionKey = Object.keys(section)[0];
              const sectionData = section[sectionKey as keyof typeof section];

              // Check if sectionData is an array before calling .every()
              if (!Array.isArray(sectionData)) {
                return null; // Skip sections that don't have array data
              }

              const fields = sectionData as any[];

              return (
                <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-base font-semibold text-gray-900 mb-2">{sectionKey}</h3>

                  {/* For checkbox sections, use grid layout */}
                  {fields.every(field => field.type === 'checkbox') ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-1">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex} className="flex items-center space-x-1">
                          {renderField(field, sectionKey)}
                          <label className="text-xs text-gray-700 leading-tight">{field.name}</label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    /* For other field types, use standard layout */
                    <div className="space-y-2">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex}>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            {field.name} {field.required && '*'}
                          </label>
                          {renderField(field, sectionKey)}
                          {(field.type === 'date' || field.type === 'time' || field.type === 'datetime') && (
                            <p className="text-xs text-gray-500 mt-1">Auto-generated</p>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => navigate(-1)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Form'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default HotWorkFormPage;
