import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useState } from 'react';
import { AlertTriangle, Calendar, ChevronDown, Filter, HardHat, Plus, Search, Upload } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import {  SiteInfo } from '../types';
import { useWorkers } from '../hooks/useGraphQL';
// import { AuditTrail } from '../components/common/AuditTrail';
import { TableSkeleton } from '../components/common/LoadingSpinner';
import SitePayrollTab from '../components/payroll/SitePayrollTab';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "<PERSON>",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date('2024-01-01T00:00:00Z')
};

// Using imported mock workers from centralized data file

const WorkerDirectory = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [activeTab, setActiveTab] = useState<'workers' | 'payroll'>('workers');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [selectedTrade, setSelectedTrade] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Fetch workers using GraphQL hook
  const { data: workersData, loading, error, refetch } = useWorkers({siteId, tenantId: 'tenant-1'});
  const workers = workersData?.workers || [];

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Workers', path: `/sites/${siteId}/workers` }
  ];

  // Handle loading and error states
  if (loading) {
    return (
      <FloatingCard title={`${site.name} - Worker Directory`} breadcrumbs={breadcrumbs}>
        <div className="mb-6">
          <div className="animate-pulse">
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="flex flex-1 items-center space-x-4">
                <div className="h-10 bg-gray-300 rounded w-64"></div>
                <div className="h-10 bg-gray-300 rounded w-20"></div>
              </div>
              <div className="flex space-x-3">
                <div className="h-10 bg-gray-300 rounded w-32"></div>
                <div className="h-10 bg-gray-300 rounded w-32"></div>
              </div>
            </div>
          </div>
        </div>
        <TableSkeleton rows={5} cols={6} />
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Error Loading Workers" breadcrumbs={[]}>
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">Failed to load workers: {error.message}</p>
          <button
            onClick={() => refetch()}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Retry
          </button>
        </div>
      </FloatingCard>
    );
  }

  // Filter workers based on search term and filters
  const filteredWorkers = workers.filter(worker => {
    // Apply search filter
    if (searchTerm && !worker.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !worker.trades.some(trade => trade.name.toLowerCase().includes(searchTerm.toLowerCase()))) {
      return false;
    }

    // Apply trade filter
    if (selectedTrade && !worker.trades.some(trade => trade.name === selectedTrade)) {
      return false;
    }

    // Apply status filter - Note: Worker interface doesn't have status field in new structure
    // We'll assume all workers are active for now
    if (selectedStatus === 'Inactive') {
      return false;
    }

    return true;
  });

  // Get unique trades for filter dropdown
  const uniqueTrades = Array.from(new Set(workers.flatMap(worker => worker.trades.map(trade => trade.name))));

  return (
    <FloatingCard title={`${site.name} - Worker Directory`} breadcrumbs={breadcrumbs}>
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`${
              activeTab === 'workers'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('workers')}
          >
            Workers
          </button>
          <button
            className={`${
              activeTab === 'payroll'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('payroll')}
          >
            Payroll
          </button>
        </nav>
      </div>

      {/* Workers Tab */}
      {activeTab === 'workers' && (
        <>
          {/* Filter and Action Bar */}
      <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
        {/* Search and Filters */}
        <div className="flex flex-1 items-center space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-500" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
              placeholder="Search workers by name or trade..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Filter Button */}
          <div className="relative">
            <button 
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              onClick={() => setFilterOpen(!filterOpen)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter
              <ChevronDown className="h-4 w-4 ml-1" />
            </button>
            
            {/* Filter Dropdown */}
            {filterOpen && (
              <div className="absolute z-10 mt-2 w-60 bg-white rounded-md shadow-lg p-4 border border-gray-200">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Trade</label>
                  <select 
                    className="w-full border border-gray-300 rounded-md p-2 text-sm"
                    value={selectedTrade || ''}
                    onChange={(e) => setSelectedTrade(e.target.value || null)}
                  >
                    <option value="">All Trades</option>
                    {uniqueTrades.map(trade => (
                      <option key={trade} value={trade}>{trade}</option>
                    ))}
                  </select>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select 
                    className="w-full border border-gray-300 rounded-md p-2 text-sm"
                    value={selectedStatus || ''}
                    onChange={(e) => setSelectedStatus(e.target.value || null)}
                  >
                    <option value="">All Statuses</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                </div>
                
                <div className="flex justify-between">
                  <button 
                    className="text-sm text-gray-600 hover:text-gray-900"
                    onClick={() => {
                      setSelectedTrade(null);
                      setSelectedStatus(null);
                    }}
                  >
                    Clear Filters
                  </button>
                  <button 
                    className="text-sm bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
                    onClick={() => setFilterOpen(false)}
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <Upload className="h-4 w-4 mr-2" />
            Import Workers
          </button>
          <Link
            to={`/sites/${siteId}/workers/create`}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New Worker
          </Link>
        </div>
      </div>
      
      {/* Workers Table */}
      <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Worker
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trades
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rating
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Man Hours
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredWorkers.map((worker) => (
              <tr key={worker.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      {worker.photoUrl ? (
                        <img className="h-10 w-10 rounded-full" src={worker.photoUrl} alt={worker.name} />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 font-medium">
                            {worker.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        <Link to={`/sites/${siteId}/workers/${worker.id}`} className="hover:text-green-500">
                          {worker.name}
                        </Link>
                      </div>
                      <div className="text-sm text-gray-500">ID: {worker.nationalId}</div>
                      <div className="text-sm text-gray-500">{worker.company}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="flex flex-wrap gap-1">
                    {worker.trades.map((trade) => (
                      <span key={trade.id} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {trade.name}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{worker.phoneNumber}</div>
                  <div className="text-sm text-gray-500">{worker.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <span className="text-sm text-gray-900 mr-2">{worker.rating.toFixed(1)}</span>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg
                          key={star}
                          className={`h-4 w-4 ${
                            star <= worker.rating ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="text-sm text-gray-900">{worker.manHours.toLocaleString()} hrs</div>
                  <div className="text-sm text-gray-500">Total worked</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-3">
                    <button
                      className="text-green-500 hover:text-green-700"
                      title="Assign Training"
                      aria-label="Assign Training"
                    >
                      <HardHat className="h-5 w-5" />
                    </button>
                    <button
                      className="text-yellow-500 hover:text-yellow-700"
                      title="Log Incident"
                      aria-label="Log Incident"
                    >
                      <AlertTriangle className="h-5 w-5" />
                    </button>
                    <button
                      className="text-blue-500 hover:text-blue-700"
                      title="View Schedule"
                      aria-label="View Schedule"
                    >
                      <Calendar className="h-5 w-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {/* Empty state */}
        {filteredWorkers.length === 0 && (
          <div className="px-6 py-10 text-center">
            <p className="text-gray-500 mb-4">No workers match your search criteria.</p>
            <button
              className="text-green-500 hover:text-green-600 font-medium"
              onClick={() => {
                setSearchTerm('');
                setSelectedTrade(null);
                setSelectedStatus(null);
              }}
            >
              Clear all filters
            </button>
          </div>
        )}
      </div>
      </>
      )}

      {/* Payroll Tab */}
      {activeTab === 'payroll' && (
        <SitePayrollTab
          workers={workers}
          siteId={siteId || ''}
          siteName={site.name}
        />
      )}
    </FloatingCard>
  );
};

export default WorkerDirectory;
