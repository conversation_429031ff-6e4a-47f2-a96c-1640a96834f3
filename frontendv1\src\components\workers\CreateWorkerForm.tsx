import React, { useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import {
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  Camera,
  Upload,
  Save,
  X,
  AlertCircle
} from 'lucide-react';
import { CREATE_WORKER } from '../../graphql/mutations';
import { GET_ALL_TRAININGS, GET_ALL_TRADES, GET_ALL_SKILLS } from '../../graphql/queries';

interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  useDummyData?: boolean; // Flag to use dummy data instead of GraphQL queries
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  gender: string;
  phoneNumber: string;
  dateOfBirth: string;
  trainingIds: number[];
  tradeIds: number[];
  skillIds: number[];
  manHours: number;
  email: string;
  inductionDate: string;
  medicalCheckDate: string;
}

interface FormErrors {
  [key: string]: string;
}

// Dummy data for dropdowns (as requested)
const getDummyTrainings = () => [
  { id: 1, name: 'Safety Induction Training' },
  { id: 2, name: 'First Aid Certification' },
  { id: 3, name: 'Equipment Operation Training' },
  { id: 4, name: 'Fire Safety Training' },
];

const getDummyTrades = () => [
  { id: 1, name: 'Electrician' },
  { id: 2, name: 'Plumber' },
  { id: 3, name: 'Carpenter' },
  { id: 4, name: 'Welder' },
  { id: 5, name: 'Mason' },
];

const getDummySkills = () => [
  { id: 1, name: 'Heavy Machinery Operation' },
  { id: 2, name: 'Blueprint Reading' },
  { id: 3, name: 'Quality Control' },
  { id: 4, name: 'Safety Compliance' },
];

const CreateWorkerForm: React.FC<CreateWorkerFormProps> = ({
  onSuccess,
  onCancel,
  useDummyData = true // Default to dummy data as requested
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: '',
    nationalId: '',
    gender: '',
    phoneNumber: '',
    dateOfBirth: '',
    trainingIds: [],
    tradeIds: [],
    skillIds: [],
    manHours: 0,
    email: '',
    inductionDate: '',
    medicalCheckDate: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [_photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);

  const [createWorker] = useMutation(CREATE_WORKER);

  // GraphQL queries for dropdown data (only fetch if not using dummy data)
  const { data: trainingsData, loading: trainingsLoading } = useQuery(GET_ALL_TRAININGS, {
    skip: useDummyData,
    errorPolicy: 'all'
  });
  const { data: tradesData, loading: tradesLoading } = useQuery(GET_ALL_TRADES, {
    skip: useDummyData,
    errorPolicy: 'all'
  });
  const { data: skillsData, loading: skillsLoading } = useQuery(GET_ALL_SKILLS, {
    skip: useDummyData,
    errorPolicy: 'all'
  });

  // Get data (dummy or from GraphQL)
  const trainings = useDummyData ? getDummyTrainings() : (trainingsData?.allTrainings || []);
  const trades = useDummyData ? getDummyTrades() : (tradesData?.allTrades || []);
  const skills = useDummyData ? getDummySkills() : (skillsData?.allSkills || []);

  const isLoadingDropdownData = !useDummyData && (trainingsLoading || tradesLoading || skillsLoading);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required field validations
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.company.trim()) newErrors.company = 'Company is required';
    if (!formData.nationalId.trim()) newErrors.nationalId = 'National ID is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';

    // Format validations
    if (formData.nationalId && !/^\d{8,12}$/.test(formData.nationalId)) {
      newErrors.nationalId = 'National ID must be 8-12 digits';
    }

    if (formData.phoneNumber && !/^[+]?[\d\s-()]{10,15}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Date validations
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 16 || age > 80) {
        newErrors.dateOfBirth = 'Age must be between 16 and 80 years';
      }
    }

    if (formData.inductionDate && new Date(formData.inductionDate) > new Date()) {
      newErrors.inductionDate = 'Induction date cannot be in the future';
    }

    if (formData.medicalCheckDate && new Date(formData.medicalCheckDate) > new Date()) {
      newErrors.medicalCheckDate = 'Medical check date cannot be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleMultiSelectChange = (field: 'trainingIds' | 'tradeIds' | 'skillIds', value: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(id => id !== value)
        : [...prev[field], value]
    }));
  };

  const handlePhotoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setPhotoFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPhotoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleTakePhoto = () => {
    // Placeholder for camera functionality
    alert('Camera functionality will be implemented later');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const result = await createWorker({
        variables: {
          name: formData.name,
          company: formData.company,
          nationalId: formData.nationalId,
          gender: formData.gender,
          phoneNumber: formData.phoneNumber,
          dateOfBirth: formData.dateOfBirth || null,
          trainingIds: formData.trainingIds,
          tradeIds: formData.tradeIds,
          skillIds: formData.skillIds,
          manHours: formData.manHours,
          email: formData.email || null,
          inductionDate: formData.inductionDate || null,
          medicalCheckDate: formData.medicalCheckDate || null,
        }
      });

      if (result.data?.createWorker) {
        onSuccess?.(result.data.createWorker);
      }
    } catch (error) {
      console.error('Error creating worker:', error);
      alert('Failed to create worker. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Create New Worker</h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Photo Upload Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-4">Worker Photo</h3>
          <div className="flex items-center space-x-4">
            {photoPreview ? (
              <img
                src={photoPreview}
                alt="Worker preview"
                className="w-24 h-24 rounded-full object-cover border-2 border-gray-300"
              />
            ) : (
              <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center">
                <User className="h-12 w-12 text-gray-400" />
              </div>
            )}

            <div className="flex flex-col space-y-2">
              <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Upload className="h-4 w-4 mr-2" />
                Upload Photo
                <input
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoSelect}
                  className="sr-only"
                />
              </label>

              <button
                type="button"
                onClick={handleTakePhoto}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Camera className="h-4 w-4 mr-2" />
                Take Photo
              </button>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Photo upload and camera functionality are independent of form validation
          </p>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="h-4 w-4 inline mr-1" />
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="Enter full name"
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Building className="h-4 w-4 inline mr-1" />
              Company *
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.company ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="Enter company name"
            />
            {errors.company && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.company}
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              National ID *
            </label>
            <input
              type="text"
              value={formData.nationalId}
              onChange={(e) => handleInputChange('nationalId', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.nationalId ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="Enter national ID"
            />
            {errors.nationalId && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.nationalId}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender *
            </label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.gender ? 'border-red-500' : 'border-gray-300'
                }`}
            >
              <option value="">Select gender</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              {/* <option value="Other">Other</option> */}
            </select>
            {errors.gender && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.gender}
              </p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="h-4 w-4 inline mr-1" />
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="+254 712 345 678"
            />
            {errors.phoneNumber && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.phoneNumber}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="h-4 w-4 inline mr-1" />
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.email}
              </p>
            )}
          </div>
        </div>

        {/* Date Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date of Birth
            </label>
            <input
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.dateOfBirth ? 'border-red-500' : 'border-gray-300'
                }`}
            />
            {errors.dateOfBirth && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.dateOfBirth}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Induction Date
            </label>
            <input
              type="date"
              value={formData.inductionDate}
              onChange={(e) => handleInputChange('inductionDate', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.inductionDate ? 'border-red-500' : 'border-gray-300'
                }`}
            />
            {errors.inductionDate && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.inductionDate}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Medical Check Date
            </label>
            <input
              type="date"
              value={formData.medicalCheckDate}
              onChange={(e) => handleInputChange('medicalCheckDate', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.medicalCheckDate ? 'border-red-500' : 'border-gray-300'
                }`}
            />
            {errors.medicalCheckDate && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.medicalCheckDate}
              </p>
            )}
          </div>
        </div>

        {/* Man Hours */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Man Hours
          </label>
          <input
            type="number"
            min="0"
            value={formData.manHours}
            onChange={(e) => handleInputChange('manHours', parseInt(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="0"
          />
        </div>

        {/* Skills, Training, and Trades Dropdowns */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Skills
            </label>
            <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading skills...</span>
                </div>
              ) : (
                skills.map((skill: { id: number, name: string }) => (
                  <label key={skill.id} className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      checked={formData.skillIds.includes(skill.id)}
                      onChange={() => handleMultiSelectChange('skillIds', skill.id)}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{skill.name}</span>
                  </label>
                ))
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Training
            </label>
            <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading trainings...</span>
                </div>
              ) : (
                trainings.map((training: { id: number, name: string }) => (
                  <label key={training.id} className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      checked={formData.trainingIds.includes(training.id)}
                      onChange={() => handleMultiSelectChange('trainingIds', training.id)}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{training.name}</span>
                  </label>
                ))
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trades
            </label>
            <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading trades...</span>
                </div>
              ) : (
                trades.map((trade: { id: number, name: string }) => (
                  <label key={trade.id} className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      checked={formData.tradeIds.includes(trade.id)}
                      onChange={() => handleMultiSelectChange('tradeIds', trade.id)}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{trade.name}</span>
                  </label>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Worker
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateWorkerForm;
