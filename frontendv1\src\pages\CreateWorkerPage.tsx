import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Users } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import CreateWorkerForm from '../components/workers/CreateWorkerForm';

const CreateWorkerPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const handleWorkerCreated = (worker: any) => {
    console.log('Worker created successfully:', worker);
    
    // Show success message (you could use a toast notification here)
    alert(`Worker "${worker.name}" has been created successfully!`);
    
    // Navigate back to the workers directory for this site
    if (siteId) {
      navigate(`/sites/${siteId}/workers`);
    } else {
      // Fallback to general workers page or dashboard
      navigate('/');
    }
  };

  const handleCancel = () => {
    // Navigate back to the workers directory
    if (siteId) {
      navigate(`/sites/${siteId}/workers`);
    } else {
      navigate('/');
    }
  };

  const handleBackClick = () => {
    handleCancel();
  };

  return (
    <FloatingCard title="Create Worker">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackClick}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Workers
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-green-600" />
                <h1 className="text-2xl font-bold text-gray-900">Create New Worker</h1>
              </div>
            </div>
          </div>
          
          {siteId && (
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Adding worker to site: <span className="font-medium">{siteId}</span>
              </p>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="px-6 py-8">
          <div className="max-w-6xl mx-auto">
            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Worker Creation Guidelines
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Fill in all required fields marked with an asterisk (*)</li>
                      <li>Upload a clear photo for identification purposes</li>
                      <li>Select relevant skills, training, and trades for the worker</li>
                      <li>Ensure all dates are accurate and not in the future</li>
                      <li>Double-check contact information for accuracy</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Create Worker Form */}
            <CreateWorkerForm
              onSuccess={handleWorkerCreated}
              onCancel={handleCancel}
              useDummyData={true} // Set to false when GraphQL backend is ready
            />
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default CreateWorkerPage;
