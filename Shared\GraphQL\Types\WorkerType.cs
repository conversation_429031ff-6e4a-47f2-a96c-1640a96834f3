﻿using HotChocolate.Types;
using HotChocolate;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class WorkerType : ObjectType<Worker>
    {
        protected override void Configure(IObjectTypeDescriptor<Worker> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Name).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Company).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.PhoneNumber).Type<NonNullType<StringType>>();

            descriptor.Field(t => t.NationalId).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Gender).Type<NonNullType<StringType>>();

            // Date fields
            descriptor.Field(t => t.DateOfBirth).Type<DateType>();
            descriptor.Field(t => t.InductionDate).Type<DateTimeType>();
            descriptor.Field(t => t.MedicalCheckDate).Type<DateTimeType>();

            // Optional fields
            descriptor.Field(t => t.PhotoUrl).Type<StringType>();
            descriptor.Field(t => t.MpesaNumber).Type<StringType>();
            descriptor.Field(t => t.Email).Type<StringType>();

            // Numeric fields
            descriptor.Field(t => t.Age).Type<IntType>(); // Computed property, can be null
            descriptor.Field(t => t.TrainingsCompleted).Type<NonNullType<IntType>>(); // Computed property
            descriptor.Field(t => t.ManHours).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Rating).Type<NonNullType<FloatType>>();

            // Navigation properties
            descriptor.Field(t => t.Trainings).Type<ListType<TrainingType>>();
            descriptor.Field(t => t.Trades).Type<ListType<TradeType>>();
            descriptor.Field(t => t.Skills).Type<ListType<SkillType>>();
            descriptor.Field(t => t.TrainingHistory).Type<ListType<WorkerTrainingHistoryType>>();
            descriptor.Field(t => t.Incidents).Type<ListType<IncidentType>>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
