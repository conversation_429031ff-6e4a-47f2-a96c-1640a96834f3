using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingConfiguration : IEntityTypeConfiguration<Training>
    {
        public void Configure(EntityTypeBuilder<Training> builder)
        {
            builder.ToTable("Trainings");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Description)
                .HasMaxLength(500);

            builder.Property(e => e.StartDate)
                .HasColumnType("datetime2");

            builder.Property(e => e.EndDate)
                .HasColumnType("datetime2");

            builder.Property(e => e.Duration)
                .HasMaxLength(50);

            builder.Property(e => e.ValidityPeriodMonths);

            builder.Property(e => e.TrainingType)
                .HasMaxLength(50);

            builder.Property(e => e.Trainer)
                .HasMaxLength(100);

            builder.Property(e => e.Frequency)
                .HasMaxLength(100);

            builder.Property(e => e.Status)
                .HasConversion<string>()
                .HasMaxLength(20)
                .HasDefaultValue(TrainingStatus.Scheduled);

            // Soft delete fields
            builder.Property(e => e.IsDeleted)
                .HasDefaultValue(false);

            builder.Property(e => e.DeletedBy)
                .HasMaxLength(100);

            // Unique constraint on Name
            builder.HasIndex(e => e.Name)
                .IsUnique();

            // Many-to-many relationship with Workers
            builder.HasMany(e => e.Workers)
                .WithMany(w => w.Trainings)
                .UsingEntity(
                    "WorkerTraining",
                    l => l.HasOne(typeof(Worker)).WithMany().HasForeignKey("WorkerId").HasPrincipalKey(nameof(Worker.Id)),
                    r => r.HasOne(typeof(Training)).WithMany().HasForeignKey("TrainingId").HasPrincipalKey(nameof(Training.Id)),
                    j => j.HasKey("WorkerId", "TrainingId"));

            // Indexes for better query performance
            builder.HasIndex(e => e.IsDeleted);
            builder.HasIndex(e => e.Status);
            builder.HasIndex(e => e.StartDate);
        }
    }
}
